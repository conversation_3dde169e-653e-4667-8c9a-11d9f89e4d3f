# Simple R script to convert ASV names using existing mapping
# Processes files in chunks to avoid memory issues

# Load required library
library(data.table)

cat("=== Simple ASV Conversion Using Existing Mapping ===\n")

# Check if mapping file exists
if (!file.exists("asv_sequence_mapping.csv")) {
  stop("Mapping file not found! Please ensure asv_sequence_mapping.csv exists.")
}

# Load the mapping
cat("Loading ASV sequence mapping...\n")
mapping <- fread("asv_sequence_mapping.csv")
cat("Loaded mapping for", nrow(mapping), "ASVs\n")

# Create lookup vector
sequence_to_code <- setNames(mapping$asv_code, mapping$sequence)

# Convert taxonomy table first (smaller file)
cat("\n=== Converting Taxonomy Table ===\n")
tryCatch({
  # Read taxonomy table
  cat("Reading taxonomy table...\n")
  tax_data <- fread("taxatable_bacteria.csv", sep = ";")
  cat("Read", nrow(tax_data), "rows\n")
  
  # Convert first column (sequences) to ASV codes
  first_col <- names(tax_data)[1]
  cat("Converting sequences in column:", first_col, "\n")
  
  # Convert sequences
  converted_count <- 0
  for (i in 1:nrow(tax_data)) {
    seq <- tax_data[[first_col]][i]
    if (seq %in% names(sequence_to_code)) {
      tax_data[[first_col]][i] <- sequence_to_code[seq]
      converted_count <- converted_count + 1
    }
    if (i %% 50000 == 0) {
      cat("Processed", i, "taxonomy entries...\n")
    }
  }
  
  cat("Converted", converted_count, "sequences in taxonomy table\n")
  
  # Save converted taxonomy table
  fwrite(tax_data, "taxatable_bacteria_converted.csv")
  cat("Taxonomy table saved as: taxatable_bacteria_converted.csv\n")
  
}, error = function(e) {
  cat("Error converting taxonomy table:", e$message, "\n")
})

# Convert ASV table using a different approach
cat("\n=== Converting ASV Table ===\n")
tryCatch({
  # Read just the first line to get headers
  cat("Reading ASV table header...\n")
  header_line <- readLines("ASV_table_bacteria.csv", n = 1)
  
  # Parse header
  header_parts <- strsplit(header_line, '","')[[1]]
  # Clean up quotes
  header_parts[1] <- gsub('^"', '', header_parts[1])
  header_parts[length(header_parts)] <- gsub('"$', '', header_parts[length(header_parts)])
  
  cat("Found", length(header_parts), "columns\n")
  
  # Convert header names
  new_header <- header_parts
  converted_cols <- 0
  
  for (i in 1:length(header_parts)) {
    if (header_parts[i] %in% names(sequence_to_code)) {
      new_header[i] <- sequence_to_code[header_parts[i]]
      converted_cols <- converted_cols + 1
    }
    if (i %% 10000 == 0) {
      cat("Processed", i, "column names...\n")
    }
  }
  
  cat("Converted", converted_cols, "column names\n")
  
  # Create new header line
  new_header_line <- paste0('"', paste(new_header, collapse = '","'), '"')
  
  # Read all lines except header
  cat("Reading ASV table data...\n")
  all_lines <- readLines("ASV_table_bacteria.csv")
  data_lines <- all_lines[-1]  # Remove header
  
  cat("Read", length(data_lines), "data rows\n")
  
  # Write converted file
  cat("Writing converted ASV table...\n")
  writeLines(c(new_header_line, data_lines), "ASV_table_bacteria_converted.csv")
  
  cat("ASV table saved as: ASV_table_bacteria_converted.csv\n")
  
}, error = function(e) {
  cat("Error converting ASV table:", e$message, "\n")
})

cat("\n=== Conversion Summary ===\n")
cat("Files created (if successful):\n")
cat("- ASV_table_bacteria_converted.csv\n")
cat("- taxatable_bacteria_converted.csv\n")
cat("- asv_sequence_mapping.csv (already existed)\n")

cat("\nConversion process completed!\n")
