# Script to convert ASV sequence names to simplified codes (ASV1, ASV2, etc.)
# while maintaining correspondence between ASV table and taxonomy table

# Load required libraries
library(data.table)
library(dplyr)

# Function to safely read large CSV files
read_large_csv <- function(file_path) {
  cat("Reading file:", file_path, "\n")
  tryCatch({
    if (file.exists(file_path)) {
      # Try reading with data.table for better performance with large files
      dt <- fread(file_path, header = TRUE, sep = ",", quote = "\"")
      cat("Successfully read", nrow(dt), "rows and", ncol(dt), "columns\n")
      return(dt)
    } else {
      stop("File does not exist: ", file_path)
    }
  }, error = function(e) {
    cat("Error reading file:", e$message, "\n")
    return(NULL)
  })
}

# Function to read taxonomy table
read_taxonomy_table <- function(file_path) {
  cat("Reading taxonomy file:", file_path, "\n")
  tryCatch({
    if (file.exists(file_path)) {
      # Read taxonomy table with semicolon separator
      tax_data <- fread(file_path, header = TRUE, sep = ";", quote = "\"")
      cat("Successfully read taxonomy table with", nrow(tax_data), "rows\n")
      return(tax_data)
    } else {
      stop("File does not exist: ", file_path)
    }
  }, error = function(e) {
    cat("Error reading taxonomy file:", e$message, "\n")
    return(NULL)
  })
}

# Main conversion function
convert_asv_names <- function() {
  cat("Starting ASV name conversion...\n")
  
  # Read the ASV table
  cat("\n=== Reading ASV Table ===\n")
  asv_table <- read_large_csv("ASV_table_bacteria.csv")
  if (is.null(asv_table)) {
    stop("Failed to read ASV table")
  }
  
  # Read the taxonomy table
  cat("\n=== Reading Taxonomy Table ===\n")
  tax_table <- read_taxonomy_table("taxatable_bacteria.csv")
  if (is.null(tax_table)) {
    stop("Failed to read taxonomy table")
  }
  
  # Get column names from ASV table (excluding the first column which is likely row names)
  asv_sequences <- colnames(asv_table)
  if (asv_sequences[1] == "" || asv_sequences[1] == "V1" || grepl("^X", asv_sequences[1])) {
    # First column is likely row identifiers, exclude it
    asv_sequences <- asv_sequences[-1]
  }
  
  cat("Found", length(asv_sequences), "ASV sequences in the table\n")
  
  # Get sequences from taxonomy table (first column)
  tax_sequences <- tax_table[[1]]
  cat("Found", length(tax_sequences), "sequences in taxonomy table\n")
  
  # Find common sequences between both tables
  common_sequences <- intersect(asv_sequences, tax_sequences)
  cat("Found", length(common_sequences), "common sequences between tables\n")
  
  if (length(common_sequences) == 0) {
    stop("No common sequences found between ASV table and taxonomy table!")
  }
  
  # Create mapping from sequences to ASV codes
  cat("\n=== Creating ASV Code Mapping ===\n")
  asv_mapping <- data.frame(
    sequence = common_sequences,
    asv_code = paste0("ASV", 1:length(common_sequences)),
    stringsAsFactors = FALSE
  )
  
  cat("Created mapping for", nrow(asv_mapping), "ASVs\n")
  
  # Save the mapping table
  write.csv(asv_mapping, "asv_sequence_mapping.csv", row.names = FALSE)
  cat("Saved ASV mapping to: asv_sequence_mapping.csv\n")
  
  # Convert ASV table column names
  cat("\n=== Converting ASV Table ===\n")
  asv_table_converted <- asv_table
  
  # Create a named vector for quick lookup
  sequence_to_code <- setNames(asv_mapping$asv_code, asv_mapping$sequence)
  
  # Convert column names
  old_colnames <- colnames(asv_table_converted)
  new_colnames <- old_colnames
  
  for (i in 1:length(old_colnames)) {
    if (old_colnames[i] %in% names(sequence_to_code)) {
      new_colnames[i] <- sequence_to_code[old_colnames[i]]
    }
  }
  
  colnames(asv_table_converted) <- new_colnames
  
  # Save converted ASV table
  write.csv(asv_table_converted, "ASV_table_bacteria_converted.csv", row.names = FALSE)
  cat("Saved converted ASV table to: ASV_table_bacteria_converted.csv\n")
  
  # Convert taxonomy table
  cat("\n=== Converting Taxonomy Table ===\n")
  tax_table_converted <- tax_table
  
  # Replace sequences with ASV codes in the first column
  first_col_name <- colnames(tax_table_converted)[1]
  tax_table_converted[[first_col_name]] <- ifelse(
    tax_table_converted[[first_col_name]] %in% names(sequence_to_code),
    sequence_to_code[tax_table_converted[[first_col_name]]],
    tax_table_converted[[first_col_name]]
  )
  
  # Save converted taxonomy table
  write.csv(tax_table_converted, "taxatable_bacteria_converted.csv", row.names = FALSE)
  cat("Saved converted taxonomy table to: taxatable_bacteria_converted.csv\n")
  
  # Print summary
  cat("\n=== Conversion Summary ===\n")
  cat("Original ASV table columns:", ncol(asv_table), "\n")
  cat("Converted ASV table columns:", ncol(asv_table_converted), "\n")
  cat("Original taxonomy entries:", nrow(tax_table), "\n")
  cat("Converted taxonomy entries:", nrow(tax_table_converted), "\n")
  cat("ASVs successfully converted:", length(common_sequences), "\n")
  
  cat("\nConversion completed successfully!\n")
  cat("Files created:\n")
  cat("- asv_sequence_mapping.csv (mapping between sequences and ASV codes)\n")
  cat("- ASV_table_bacteria_converted.csv (ASV table with simplified names)\n")
  cat("- taxatable_bacteria_converted.csv (taxonomy table with simplified names)\n")
  
  return(list(
    mapping = asv_mapping,
    asv_table = asv_table_converted,
    taxonomy = tax_table_converted
  ))
}

# Run the conversion
cat("=== ASV Name Conversion Script ===\n")
cat("This script will convert long DNA sequence names to simplified ASV codes\n")
cat("while maintaining correspondence between ASV and taxonomy tables.\n\n")

result <- convert_asv_names()
