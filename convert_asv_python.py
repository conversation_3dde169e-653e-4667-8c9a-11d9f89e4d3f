#!/usr/bin/env python3
"""
Python script to convert ASV sequence names to simplified codes
using the existing mapping file. More memory efficient than R for large files.
"""

import pandas as pd
import csv
import sys
from pathlib import Path

def load_mapping():
    """Load the ASV sequence mapping."""
    print("Loading ASV sequence mapping...")
    try:
        mapping_df = pd.read_csv("asv_sequence_mapping.csv")
        print(f"Loaded mapping for {len(mapping_df)} ASVs")
        
        # Create dictionary for fast lookup
        sequence_to_code = dict(zip(mapping_df['sequence'], mapping_df['asv_code']))
        return sequence_to_code
    except Exception as e:
        print(f"Error loading mapping: {e}")
        return None

def convert_asv_table(sequence_to_code):
    """Convert ASV table column names from sequences to ASV codes."""
    print("\n=== Converting ASV Table ===")
    
    try:
        # Read the header first to get column names
        print("Reading ASV table header...")
        with open("ASV_table_bacteria.csv", 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            header = next(reader)
        
        print(f"Found {len(header)} columns in ASV table")
        
        # Convert column names
        new_header = []
        converted_count = 0
        
        for i, col in enumerate(header):
            if col in sequence_to_code:
                new_header.append(sequence_to_code[col])
                converted_count += 1
            else:
                new_header.append(col)
            
            if (i + 1) % 10000 == 0:
                print(f"Processed {i + 1} column names...")
        
        print(f"Column name conversion completed. Converted {converted_count} sequences.")
        
        # Read the data in chunks to avoid memory issues
        print("Reading and converting ASV table data...")
        chunk_size = 1000  # Process 1000 rows at a time
        
        first_chunk = True
        output_file = "ASV_table_bacteria_converted.csv"
        
        for chunk in pd.read_csv("ASV_table_bacteria.csv", chunksize=chunk_size):
            # Set new column names
            chunk.columns = new_header
            
            # Write to output file
            if first_chunk:
                chunk.to_csv(output_file, index=False, mode='w')
                first_chunk = False
            else:
                chunk.to_csv(output_file, index=False, mode='a', header=False)
        
        print(f"ASV table conversion completed!")
        print(f"File saved as: {output_file}")
        return True
        
    except Exception as e:
        print(f"Error converting ASV table: {e}")
        return False

def convert_taxonomy_table(sequence_to_code):
    """Convert taxonomy table sequence names to ASV codes."""
    print("\n=== Converting Taxonomy Table ===")
    
    try:
        # Read taxonomy table
        print("Reading taxonomy table...")
        tax_df = pd.read_csv("taxatable_bacteria.csv", sep=';')
        
        print(f"Read taxonomy table with {len(tax_df)} rows")
        
        # Get the first column name (contains sequences)
        first_col = tax_df.columns[0]
        print(f"Converting sequences in column: {first_col}")
        
        # Convert sequences to ASV codes
        converted_count = 0
        for i, seq in enumerate(tax_df[first_col]):
            if seq in sequence_to_code:
                tax_df.iloc[i, 0] = sequence_to_code[seq]
                converted_count += 1
        
        print(f"Converted {converted_count} sequences in taxonomy table")
        
        # Save converted taxonomy table
        output_file = "taxatable_bacteria_converted.csv"
        tax_df.to_csv(output_file, index=False)
        
        print(f"Taxonomy table conversion completed!")
        print(f"File saved as: {output_file}")
        return True
        
    except Exception as e:
        print(f"Error converting taxonomy table: {e}")
        return False

def main():
    """Main conversion process."""
    print("=== Python ASV Name Conversion ===")
    print("Converting ASV sequence names to simplified codes...")
    
    # Check if mapping file exists
    if not Path("asv_sequence_mapping.csv").exists():
        print("Error: asv_sequence_mapping.csv not found!")
        print("Please run the R script first to create the mapping.")
        return False
    
    # Load mapping
    sequence_to_code = load_mapping()
    if sequence_to_code is None:
        return False
    
    # Convert ASV table
    asv_success = convert_asv_table(sequence_to_code)
    
    # Convert taxonomy table
    tax_success = convert_taxonomy_table(sequence_to_code)
    
    # Summary
    print("\n=== Conversion Summary ===")
    if asv_success:
        print("✓ ASV table converted successfully")
    else:
        print("✗ ASV table conversion failed")
    
    if tax_success:
        print("✓ Taxonomy table converted successfully")
    else:
        print("✗ Taxonomy table conversion failed")
    
    if asv_success and tax_success:
        print("\n🎉 All conversions completed successfully!")
        print("\nFiles created:")
        print("- asv_sequence_mapping.csv (mapping between sequences and ASV codes)")
        print("- ASV_table_bacteria_converted.csv (ASV table with simplified names)")
        print("- taxatable_bacteria_converted.csv (taxonomy table with simplified names)")
        return True
    else:
        print("\n❌ Some conversions failed. Check the error messages above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
