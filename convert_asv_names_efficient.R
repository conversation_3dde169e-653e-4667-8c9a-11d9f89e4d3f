# Memory-efficient script to convert ASV sequence names to simplified codes
# Processes large files without loading everything into memory at once

# Load required libraries
library(data.table)

# Function to read and process ASV table efficiently
convert_asv_table_efficient <- function() {
  cat("=== Memory-Efficient ASV Conversion ===\n")
  
  # First, load the mapping that was already created
  if (!file.exists("asv_sequence_mapping.csv")) {
    stop("Mapping file not found. Please run the main conversion script first.")
  }
  
  cat("Loading ASV sequence mapping...\n")
  mapping <- fread("asv_sequence_mapping.csv")
  cat("Loaded mapping for", nrow(mapping), "ASVs\n")
  
  # Create lookup vector
  sequence_to_code <- setNames(mapping$asv_code, mapping$sequence)
  
  # Read just the header of the ASV table to get column names
  cat("Reading ASV table header...\n")
  header_line <- readLines("ASV_table_bacteria.csv", n = 1)
  
  # Parse the header to get column names
  header_cols <- strsplit(header_line, ",")[[1]]
  # Remove quotes if present
  header_cols <- gsub('"', '', header_cols)
  
  cat("Found", length(header_cols), "columns in ASV table\n")
  
  # Convert column names
  new_header_cols <- header_cols
  for (i in 1:length(header_cols)) {
    if (header_cols[i] %in% names(sequence_to_code)) {
      new_header_cols[i] <- sequence_to_code[header_cols[i]]
      if (i %% 10000 == 0) {
        cat("Processed", i, "column names...\n")
      }
    }
  }
  
  cat("Column name conversion completed\n")
  
  # Read the ASV table with data.table for efficiency
  cat("Reading ASV table data...\n")
  asv_data <- fread("ASV_table_bacteria.csv", header = TRUE)
  
  # Set the new column names
  setnames(asv_data, old = names(asv_data), new = new_header_cols)
  
  # Write the converted table
  cat("Writing converted ASV table...\n")
  fwrite(asv_data, "ASV_table_bacteria_converted.csv")
  
  cat("ASV table conversion completed!\n")
  cat("File saved as: ASV_table_bacteria_converted.csv\n")
  
  return(TRUE)
}

# Function to convert taxonomy table
convert_taxonomy_table <- function() {
  cat("\n=== Converting Taxonomy Table ===\n")
  
  # Load the mapping
  if (!file.exists("asv_sequence_mapping.csv")) {
    stop("Mapping file not found.")
  }
  
  mapping <- fread("asv_sequence_mapping.csv")
  sequence_to_code <- setNames(mapping$asv_code, mapping$sequence)
  
  # Read taxonomy table
  cat("Reading taxonomy table...\n")
  tax_data <- fread("taxatable_bacteria.csv", sep = ";")
  
  # Get the first column name
  first_col <- names(tax_data)[1]
  cat("Converting sequences in column:", first_col, "\n")
  
  # Convert sequences to ASV codes
  tax_data[[first_col]] <- ifelse(
    tax_data[[first_col]] %in% names(sequence_to_code),
    sequence_to_code[tax_data[[first_col]]],
    tax_data[[first_col]]
  )
  
  # Write converted taxonomy table
  cat("Writing converted taxonomy table...\n")
  fwrite(tax_data, "taxatable_bacteria_converted.csv")
  
  cat("Taxonomy table conversion completed!\n")
  cat("File saved as: taxatable_bacteria_converted.csv\n")
  
  return(TRUE)
}

# Main execution
cat("Starting efficient ASV conversion process...\n\n")

# Check if mapping exists, if not create it first
if (!file.exists("asv_sequence_mapping.csv")) {
  cat("Mapping file not found. Creating mapping first...\n")
  
  # Read taxonomy table to get all sequences
  cat("Reading taxonomy table for mapping...\n")
  tax_data <- fread("taxatable_bacteria.csv", sep = ";")
  tax_sequences <- tax_data[[1]]
  
  # Read ASV table header only
  header_line <- readLines("ASV_table_bacteria.csv", n = 1)
  header_cols <- strsplit(header_line, ",")[[1]]
  header_cols <- gsub('"', '', header_cols)
  
  # Remove first column if it's row names
  if (header_cols[1] == "" || header_cols[1] == "V1" || grepl("^X", header_cols[1])) {
    asv_sequences <- header_cols[-1]
  } else {
    asv_sequences <- header_cols
  }
  
  # Find common sequences
  common_sequences <- intersect(asv_sequences, tax_sequences)
  cat("Found", length(common_sequences), "common sequences\n")
  
  # Create mapping
  mapping <- data.frame(
    sequence = common_sequences,
    asv_code = paste0("ASV", 1:length(common_sequences)),
    stringsAsFactors = FALSE
  )
  
  # Save mapping
  fwrite(mapping, "asv_sequence_mapping.csv")
  cat("Mapping saved to: asv_sequence_mapping.csv\n\n")
}

# Convert ASV table
tryCatch({
  convert_asv_table_efficient()
}, error = function(e) {
  cat("Error converting ASV table:", e$message, "\n")
})

# Convert taxonomy table
tryCatch({
  convert_taxonomy_table()
}, error = function(e) {
  cat("Error converting taxonomy table:", e$message, "\n")
})

cat("\n=== Conversion Process Completed ===\n")
cat("Check the following files:\n")
cat("- asv_sequence_mapping.csv (sequence to ASV code mapping)\n")
cat("- ASV_table_bacteria_converted.csv (converted ASV table)\n")
cat("- taxatable_bacteria_converted.csv (converted taxonomy table)\n")
